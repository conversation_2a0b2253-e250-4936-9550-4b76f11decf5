{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { INVOICE_COLS } from './invoice-cols';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/core/services/customer.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@app/core/services/exex-common.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/toolbar\";\nimport * as i7 from \"primeng/dialog\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"primeng/fileupload\";\nimport * as i11 from \"primeng/keyfilter\";\nfunction InvoiceComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵelement(1, \"i\", 8)(2, \"input\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoiceComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 10);\n    i0.ɵɵlistener(\"onClick\", function InvoiceComponent_ng_template_2_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openNew());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 11);\n    i0.ɵɵlistener(\"onClick\", function InvoiceComponent_ng_template_2_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.deleteSelectedProducts());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"p-fileUpload\", 12)(3, \"p-button\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.selectedInvoices || !ctx_r1.selectedInvoices.length);\n  }\n}\nfunction InvoiceComponent_ng_template_5_small_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoiceComponent_ng_template_5_small_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \" Total Amount must be a positive number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoiceComponent_ng_template_5_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \" Paid Amount must be a positive number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoiceComponent_ng_template_5_small_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \" Remaining Amount must be a positive number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InvoiceComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 14)(1, \"div\", 15)(2, \"label\", 16);\n    i0.ɵɵtext(3, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 17);\n    i0.ɵɵtemplate(5, InvoiceComponent_ng_template_5_small_5_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 15)(7, \"label\", 19);\n    i0.ɵɵtext(8, \"Total Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 20);\n    i0.ɵɵtemplate(10, InvoiceComponent_ng_template_5_small_10_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 15)(12, \"label\", 21);\n    i0.ɵɵtext(13, \"Paid Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 22);\n    i0.ɵɵtemplate(15, InvoiceComponent_ng_template_5_small_15_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 15)(17, \"label\", 23);\n    i0.ɵɵtext(18, \"Remaining Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 24);\n    i0.ɵɵtemplate(20, InvoiceComponent_ng_template_5_small_20_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.invoiceForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r2.invoiceForm.get(\"status\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r2.invoiceForm.get(\"status\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.invoiceForm.get(\"totalAmount\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r2.invoiceForm.get(\"totalAmount\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r2.invoiceForm.get(\"paidAmount\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r2.invoiceForm.get(\"paidAmount\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.invoiceForm.get(\"remainingAmount\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.invoiceForm.get(\"remainingAmount\")) == null ? null : tmp_4_0.touched));\n  }\n}\nfunction InvoiceComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 26);\n    i0.ɵɵlistener(\"onClick\", function InvoiceComponent_ng_template_6_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.hideDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 27);\n    i0.ɵɵlistener(\"onClick\", function InvoiceComponent_ng_template_6_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.saveCustomer());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"text\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"text\", true)(\"disabled\", ctx_r3.invoiceForm.invalid);\n  }\n}\nconst _c0 = () => ({\n  width: \"450px\"\n});\nexport class InvoiceComponent {\n  constructor(customerService, fb, exexCommonService) {\n    this.customerService = customerService;\n    this.fb = fb;\n    this.exexCommonService = exexCommonService;\n    this.invoiceDialog = false;\n    this.heightTableScroll = 0;\n  }\n  ngOnInit() {\n    this.dataTable = {\n      ...this.dataTable,\n      columns: INVOICE_COLS\n    };\n    this.customerService.getInvoices().then(data => {\n      this.dataTable = {\n        ...this.dataTable,\n        value: data\n      };\n    });\n    this.invoiceForm = this.fb.group({\n      status: ['', Validators.required],\n      totalAmount: [null, [Validators.required, Validators.min(0)]],\n      paidAmount: [null, [Validators.required, Validators.min(0)]],\n      remainingAmount: [null, [Validators.required, Validators.min(0)]]\n    });\n  }\n  openNew() {\n    this.invoiceForm.reset();\n    this.invoice = {};\n    this.invoiceDialog = true;\n  }\n  deleteSelectedProducts() {\n    this.exexCommonService.showDialogConfirm(() => {\n      this.dataTable.value = this.dataTable.value.filter(val => !this.selectedInvoices?.includes(val));\n      this.selectedInvoices = null;\n      this.exexCommonService.showToastSuccess('Customers Deleted');\n    });\n  }\n  selectedRow(rows) {\n    this.selectedInvoices = [...rows];\n  }\n  editProduct(invoice) {\n    this.invoice = {\n      ...invoice\n    };\n    this.invoiceForm.patchValue({\n      status: this.invoice.status,\n      totalAmount: this.invoice.totalAmount,\n      paidAmount: this.invoice.paidAmount,\n      remainingAmount: this.invoice.remainingAmount\n    });\n    this.invoiceDialog = true;\n  }\n  deleteProduct(invoice) {\n    this.exexCommonService.showDialogConfirm(() => {\n      this.dataTable.value = this.dataTable.value.filter(val => val.customerId !== invoice.customerId);\n      this.invoice = {};\n      this.exexCommonService.showToastSuccess('Customer Deleted');\n    });\n  }\n  hideDialog() {\n    this.invoiceDialog = false;\n  }\n  saveCustomer() {\n    if (this.invoiceForm.valid) {\n      if (this.invoice.customerId) {\n        this.dataTable.value[this.findIndexById(this.invoice.customerId)] = this.invoice;\n        this.exexCommonService.showToastSuccess('Successful');\n      } else {\n        this.dataTable.value.push(this.invoice);\n        this.exexCommonService.showToastSuccess('Successful');\n      }\n      this.dataTable.value = [...this.dataTable.value];\n      this.invoiceDialog = false;\n      this.invoice = {};\n    } else {\n      this.invoiceForm.markAllAsTouched(); // Show validation errors\n    }\n  }\n\n  findIndexById(customerId) {\n    let index = -1;\n    for (let i = 0; i < this.dataTable.value.length; i++) {\n      if (this.dataTable.value[i].customerId === customerId) {\n        index = i;\n        break;\n      }\n    }\n    return index;\n  }\n  static #_ = this.ɵfac = function InvoiceComponent_Factory(t) {\n    return new (t || InvoiceComponent)(i0.ɵɵdirectiveInject(i1.CustomerService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ExexCommonService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: InvoiceComponent,\n    selectors: [[\"app-invoice\"]],\n    decls: 7,\n    vars: 6,\n    consts: [[\"styleClass\", \"mb-3\"], [\"pTemplate\", \"left\"], [\"pTemplate\", \"right\"], [3, \"propExexTable\", \"selectedEvent\", \"editEvent\", \"deleteEvent\"], [\"header\", \"Invoice Details\", \"styleClass\", \"p-fluid\", 3, \"visible\", \"modal\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search...\"], [\"severity\", \"success\", \"label\", \"New\", \"icon\", \"pi pi-plus\", 1, \"mr-2\", 3, \"onClick\"], [\"severity\", \"danger\", \"label\", \"Delete\", \"icon\", \"pi pi-trash\", 1, \"mr-2\", 3, \"disabled\", \"onClick\"], [\"mode\", \"basic\", \"accept\", \".csv,.xls,.xlsx\", \"maxFileSize\", \"5000000\", \"label\", \"Import\", \"chooseLabel\", \"Import\", 1, \"mr-2\", \"inline-block\"], [\"severity\", \"help\", \"label\", \"Export\", \"icon\", \"pi pi-upload\"], [3, \"formGroup\"], [1, \"field\"], [\"for\", \"status\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"status\", \"formControlName\", \"status\", \"autofocus\", \"\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"totalAmount\"], [\"pInputText\", \"\", \"pKeyFilter\", \"num\", \"id\", \"totalAmount\", \"formControlName\", \"totalAmount\"], [\"for\", \"paidAmount\"], [\"pInputText\", \"\", \"pKeyFilter\", \"num\", \"id\", \"paidAmount\", \"formControlName\", \"paidAmount\"], [\"for\", \"remainingAmount\"], [\"pInputText\", \"\", \"pKeyFilter\", \"num\", \"id\", \"remainingAmount\", \"formControlName\", \"remainingAmount\"], [1, \"p-error\"], [\"label\", \"Cancel\", \"icon\", \"pi pi-times\", 3, \"text\", \"onClick\"], [\"label\", \"Save\", \"icon\", \"pi pi-check\", 3, \"text\", \"disabled\", \"onClick\"]],\n    template: function InvoiceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-toolbar\", 0);\n        i0.ɵɵtemplate(1, InvoiceComponent_ng_template_1_Template, 3, 0, \"ng-template\", 1)(2, InvoiceComponent_ng_template_2_Template, 4, 1, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"exex-table\", 3);\n        i0.ɵɵlistener(\"selectedEvent\", function InvoiceComponent_Template_exex_table_selectedEvent_3_listener($event) {\n          return ctx.selectedRow($event);\n        })(\"editEvent\", function InvoiceComponent_Template_exex_table_editEvent_3_listener($event) {\n          return ctx.editProduct($event);\n        })(\"deleteEvent\", function InvoiceComponent_Template_exex_table_deleteEvent_3_listener($event) {\n          return ctx.deleteProduct($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"p-dialog\", 4);\n        i0.ɵɵlistener(\"visibleChange\", function InvoiceComponent_Template_p_dialog_visibleChange_4_listener($event) {\n          return ctx.invoiceDialog = $event;\n        });\n        i0.ɵɵtemplate(5, InvoiceComponent_ng_template_5_Template, 21, 5, \"ng-template\", 5)(6, InvoiceComponent_ng_template_6_Template, 2, 3, \"ng-template\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"propExexTable\", ctx.dataTable);\n        i0.ɵɵadvance(1);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(5, _c0));\n        i0.ɵɵproperty(\"visible\", ctx.invoiceDialog)(\"modal\", true);\n      }\n    },\n    dependencies: [i4.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i5.PrimeTemplate, i6.Toolbar, i7.Dialog, i8.Button, i9.InputText, i10.FileUpload, i11.KeyFilter],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "INVOICE_COLS", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "InvoiceComponent_ng_template_2_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "openNew", "InvoiceComponent_ng_template_2_Template_p_button_onClick_1_listener", "ctx_r6", "deleteSelectedProducts", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "selectedInvoices", "length", "ɵɵtext", "ɵɵtemplate", "InvoiceComponent_ng_template_5_small_5_Template", "InvoiceComponent_ng_template_5_small_10_Template", "InvoiceComponent_ng_template_5_small_15_Template", "InvoiceComponent_ng_template_5_small_20_Template", "ctx_r2", "invoiceForm", "tmp_1_0", "get", "invalid", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "InvoiceComponent_ng_template_6_Template_p_button_onClick_0_listener", "_r12", "ctx_r11", "hideDialog", "InvoiceComponent_ng_template_6_Template_p_button_onClick_1_listener", "ctx_r13", "saveCustomer", "ctx_r3", "InvoiceComponent", "constructor", "customerService", "fb", "exexCommonService", "invoiceDialog", "heightTableScroll", "ngOnInit", "dataTable", "columns", "getInvoices", "then", "data", "value", "group", "status", "required", "totalAmount", "min", "paidAmount", "remainingAmount", "reset", "invoice", "showDialogConfirm", "filter", "val", "includes", "showToastSuccess", "selectedRow", "rows", "editProduct", "patchValue", "deleteProduct", "customerId", "valid", "findIndexById", "push", "mark<PERSON>llAsTouched", "index", "i", "_", "ɵɵdirectiveInject", "i1", "CustomerService", "i2", "FormBuilder", "i3", "ExexCommonService", "_2", "selectors", "decls", "vars", "consts", "template", "InvoiceComponent_Template", "rf", "ctx", "InvoiceComponent_ng_template_1_Template", "InvoiceComponent_ng_template_2_Template", "InvoiceComponent_Template_exex_table_selectedEvent_3_listener", "$event", "InvoiceComponent_Template_exex_table_editEvent_3_listener", "InvoiceComponent_Template_exex_table_deleteEvent_3_listener", "InvoiceComponent_Template_p_dialog_visibleChange_4_listener", "InvoiceComponent_ng_template_5_Template", "InvoiceComponent_ng_template_6_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\invoice\\invoice.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\invoice\\invoice.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { IPropExexTable } from '../../common/exex-table/exex-table.model';\r\nimport { INVOICE_COLS } from './invoice-cols';\r\nimport { CustomerService } from '@app/core/services/customer.service';\r\nimport { ExexCommonService } from '@app/core/services/exex-common.service';\r\n\r\n@Component({\r\n    selector: 'app-invoice',\r\n    templateUrl: './invoice.component.html',\r\n    styleUrl: './invoice.component.scss',\r\n})\r\nexport class InvoiceComponent {\r\n    invoiceDialog: boolean = false;\r\n\r\n    invoices!: any[];\r\n\r\n    invoice!: any;\r\n\r\n    selectedInvoices!: any[] | null;\r\n\r\n    cols: any;\r\n\r\n    heightTableScroll: number = 0;\r\n\r\n    invoiceForm!: FormGroup;\r\n\r\n    dataTable!: IPropExexTable;\r\n\r\n    constructor(\r\n        private customerService: CustomerService,\r\n        private fb: FormBuilder,\r\n        private exexCommonService: ExexCommonService,\r\n    ) {}\r\n\r\n    ngOnInit() {\r\n        this.dataTable = {\r\n            ...this.dataTable,\r\n            columns: INVOICE_COLS,\r\n        };\r\n        this.customerService.getInvoices().then((data) => {\r\n            this.dataTable = {\r\n                ...this.dataTable,\r\n                value: data,\r\n            };\r\n        });\r\n\r\n        this.invoiceForm = this.fb.group({\r\n            status: ['', Validators.required],\r\n            totalAmount: [null, [Validators.required, Validators.min(0)]],\r\n            paidAmount: [null, [Validators.required, Validators.min(0)]],\r\n            remainingAmount: [null, [Validators.required, Validators.min(0)]],\r\n        });\r\n    }\r\n\r\n    openNew() {\r\n        this.invoiceForm.reset();\r\n        this.invoice = {};\r\n        this.invoiceDialog = true;\r\n    }\r\n\r\n    deleteSelectedProducts() {\r\n        this.exexCommonService.showDialogConfirm(() => {\r\n            this.dataTable.value = this.dataTable.value.filter((val) => !this.selectedInvoices?.includes(val));\r\n            this.selectedInvoices = null;\r\n            this.exexCommonService.showToastSuccess('Customers Deleted');\r\n        });\r\n    }\r\n\r\n    selectedRow(rows) {\r\n        this.selectedInvoices = [...rows];\r\n    }\r\n\r\n    editProduct(invoice: any) {\r\n        this.invoice = { ...invoice };\r\n        this.invoiceForm.patchValue({\r\n            status: this.invoice.status,\r\n            totalAmount: this.invoice.totalAmount,\r\n            paidAmount: this.invoice.paidAmount,\r\n            remainingAmount: this.invoice.remainingAmount,\r\n        });\r\n        this.invoiceDialog = true;\r\n    }\r\n\r\n    deleteProduct(invoice: any) {\r\n        this.exexCommonService.showDialogConfirm(() => {\r\n            this.dataTable.value = this.dataTable.value.filter((val) => val.customerId !== invoice.customerId);\r\n            this.invoice = {};\r\n            this.exexCommonService.showToastSuccess('Customer Deleted');\r\n        });\r\n    }\r\n\r\n    hideDialog() {\r\n        this.invoiceDialog = false;\r\n    }\r\n\r\n    saveCustomer() {\r\n        if (this.invoiceForm.valid) {\r\n            if (this.invoice.customerId) {\r\n                this.dataTable.value[this.findIndexById(this.invoice.customerId)] = this.invoice;\r\n                this.exexCommonService.showToastSuccess('Successful');\r\n            } else {\r\n                this.dataTable.value.push(this.invoice);\r\n                this.exexCommonService.showToastSuccess('Successful');\r\n            }\r\n\r\n            this.dataTable.value = [...this.dataTable.value];\r\n            this.invoiceDialog = false;\r\n            this.invoice = {};\r\n        } else {\r\n            this.invoiceForm.markAllAsTouched(); // Show validation errors\r\n        }\r\n    }\r\n\r\n    findIndexById(customerId: string): number {\r\n        let index = -1;\r\n        for (let i = 0; i < this.dataTable.value.length; i++) {\r\n            if (this.dataTable.value[i].customerId === customerId) {\r\n                index = i;\r\n                break;\r\n            }\r\n        }\r\n        return index;\r\n    }\r\n}\r\n", "<p-toolbar styleClass=\"mb-3\">\r\n    <ng-template pTemplate=\"left\">\r\n        <span class=\"p-input-icon-left\">\r\n            <i class=\"pi pi-search\"></i>\r\n            <input pInputText type=\"text\" placeholder=\"Search...\" />\r\n        </span>\r\n    </ng-template>\r\n\r\n    <ng-template pTemplate=\"right\">\r\n        <p-button severity=\"success\" label=\"New\" icon=\"pi pi-plus\" class=\"mr-2\" (onClick)=\"openNew()\" />\r\n        <p-button\r\n            severity=\"danger\"\r\n            label=\"Delete\"\r\n            icon=\"pi pi-trash\"\r\n            class=\"mr-2\"\r\n            (onClick)=\"deleteSelectedProducts()\"\r\n            [disabled]=\"!selectedInvoices || !selectedInvoices.length\" />\r\n\r\n        <p-fileUpload\r\n            mode=\"basic\"\r\n            accept=\".csv,.xls,.xlsx\"\r\n            maxFileSize=\"5000000\"\r\n            label=\"Import\"\r\n            chooseLabel=\"Import\"\r\n            class=\"mr-2 inline-block\" />\r\n        <p-button severity=\"help\" label=\"Export\" icon=\"pi pi-upload\" />\r\n    </ng-template>\r\n</p-toolbar>\r\n\r\n<exex-table\r\n    [propExexTable]=\"dataTable\"\r\n    (selectedEvent)=\"selectedRow($event)\"\r\n    (editEvent)=\"editProduct($event)\"\r\n    (deleteEvent)=\"deleteProduct($event)\"></exex-table>\r\n\r\n<p-dialog\r\n    [(visible)]=\"invoiceDialog\"\r\n    [style]=\"{ width: '450px' }\"\r\n    header=\"Invoice Details\"\r\n    [modal]=\"true\"\r\n    styleClass=\"p-fluid\">\r\n    <ng-template pTemplate=\"content\">\r\n        <form [formGroup]=\"invoiceForm\">\r\n            <div class=\"field\">\r\n                <label for=\"status\">Status</label>\r\n                <input type=\"text\" pInputText id=\"status\" formControlName=\"status\" autofocus />\r\n                <small class=\"p-error\" *ngIf=\"invoiceForm.get('status')?.invalid && invoiceForm.get('status')?.touched\">\r\n                    Status is required.\r\n                </small>\r\n            </div>\r\n\r\n            <div class=\"field\">\r\n                <label for=\"totalAmount\">Total Amount</label>\r\n                <input pInputText pKeyFilter=\"num\" id=\"totalAmount\" formControlName=\"totalAmount\" />\r\n                <small\r\n                    class=\"p-error\"\r\n                    *ngIf=\"invoiceForm.get('totalAmount')?.invalid && invoiceForm.get('totalAmount')?.touched\">\r\n                    Total Amount must be a positive number.\r\n                </small>\r\n            </div>\r\n\r\n            <div class=\"field\">\r\n                <label for=\"paidAmount\">Paid Amount</label>\r\n                <input pInputText pKeyFilter=\"num\" id=\"paidAmount\" formControlName=\"paidAmount\" />\r\n                <small\r\n                    class=\"p-error\"\r\n                    *ngIf=\"invoiceForm.get('paidAmount')?.invalid && invoiceForm.get('paidAmount')?.touched\">\r\n                    Paid Amount must be a positive number.\r\n                </small>\r\n            </div>\r\n            <div class=\"field\">\r\n                <label for=\"remainingAmount\">Remaining Amount</label>\r\n                <input pInputText pKeyFilter=\"num\" id=\"remainingAmount\" formControlName=\"remainingAmount\" />\r\n                <small\r\n                    class=\"p-error\"\r\n                    *ngIf=\"invoiceForm.get('remainingAmount')?.invalid && invoiceForm.get('remainingAmount')?.touched\">\r\n                    Remaining Amount must be a positive number.\r\n                </small>\r\n            </div>\r\n        </form>\r\n    </ng-template>\r\n\r\n    <ng-template pTemplate=\"footer\">\r\n        <p-button label=\"Cancel\" icon=\"pi pi-times\" [text]=\"true\" (onClick)=\"hideDialog()\" />\r\n        <p-button\r\n            label=\"Save\"\r\n            icon=\"pi pi-check\"\r\n            [text]=\"true\"\r\n            (onClick)=\"saveCustomer()\"\r\n            [disabled]=\"invoiceForm.invalid\" />\r\n    </ng-template>\r\n</p-dialog>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICDrCC,EAAA,CAAAC,cAAA,cAAgC;IAC5BD,EAAA,CAAAE,SAAA,WAA4B;IAEhCF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAIPH,EAAA,CAAAC,cAAA,mBAAgG;IAAxBD,EAAA,CAAAI,UAAA,qBAAAC,oEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAA7FX,EAAA,CAAAG,YAAA,EAAgG;IAChGH,EAAA,CAAAC,cAAA,mBAMiE;IAD7DD,EAAA,CAAAI,UAAA,qBAAAQ,oEAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAb,EAAA,CAAAS,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAAG,MAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IALxCd,EAAA,CAAAG,YAAA,EAMiE;IAEjEH,EAAA,CAAAE,SAAA,uBAMgC;;;;IAR5BF,EAAA,CAAAe,SAAA,GAA0D;IAA1Df,EAAA,CAAAgB,UAAA,cAAAC,MAAA,CAAAC,gBAAA,KAAAD,MAAA,CAAAC,gBAAA,CAAAC,MAAA,CAA0D;;;;;IA8BtDnB,EAAA,CAAAC,cAAA,gBAAwG;IACpGD,EAAA,CAAAoB,MAAA,4BACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMRH,EAAA,CAAAC,cAAA,gBAE+F;IAC3FD,EAAA,CAAAoB,MAAA,gDACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMRH,EAAA,CAAAC,cAAA,gBAE6F;IACzFD,EAAA,CAAAoB,MAAA,+CACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAKRH,EAAA,CAAAC,cAAA,gBAEuG;IACnGD,EAAA,CAAAoB,MAAA,oDACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAnChBH,EAAA,CAAAC,cAAA,eAAgC;IAEJD,EAAA,CAAAoB,MAAA,aAAM;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IAClCH,EAAA,CAAAE,SAAA,gBAA+E;IAC/EF,EAAA,CAAAqB,UAAA,IAAAC,+CAAA,oBAEQ;IACZtB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAmB;IACUD,EAAA,CAAAoB,MAAA,mBAAY;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IAC7CH,EAAA,CAAAE,SAAA,gBAAoF;IACpFF,EAAA,CAAAqB,UAAA,KAAAE,gDAAA,oBAIQ;IACZvB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAmB;IACSD,EAAA,CAAAoB,MAAA,mBAAW;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IAC3CH,EAAA,CAAAE,SAAA,iBAAkF;IAClFF,EAAA,CAAAqB,UAAA,KAAAG,gDAAA,oBAIQ;IACZxB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmB;IACcD,EAAA,CAAAoB,MAAA,wBAAgB;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IACrDH,EAAA,CAAAE,SAAA,iBAA4F;IAC5FF,EAAA,CAAAqB,UAAA,KAAAI,gDAAA,oBAIQ;IACZzB,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IApCJH,EAAA,CAAAgB,UAAA,cAAAU,MAAA,CAAAC,WAAA,CAAyB;IAIC3B,EAAA,CAAAe,SAAA,GAA8E;IAA9Ef,EAAA,CAAAgB,UAAA,WAAAY,OAAA,GAAAF,MAAA,CAAAC,WAAA,CAAAE,GAAA,6BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAF,MAAA,CAAAC,WAAA,CAAAE,GAAA,6BAAAD,OAAA,CAAAG,OAAA,EAA8E;IAUjG/B,EAAA,CAAAe,SAAA,GAAwF;IAAxFf,EAAA,CAAAgB,UAAA,WAAAgB,OAAA,GAAAN,MAAA,CAAAC,WAAA,CAAAE,GAAA,kCAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAN,MAAA,CAAAC,WAAA,CAAAE,GAAA,kCAAAG,OAAA,CAAAD,OAAA,EAAwF;IAUxF/B,EAAA,CAAAe,SAAA,GAAsF;IAAtFf,EAAA,CAAAgB,UAAA,WAAAiB,OAAA,GAAAP,MAAA,CAAAC,WAAA,CAAAE,GAAA,iCAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAP,MAAA,CAAAC,WAAA,CAAAE,GAAA,iCAAAI,OAAA,CAAAF,OAAA,EAAsF;IAStF/B,EAAA,CAAAe,SAAA,GAAgG;IAAhGf,EAAA,CAAAgB,UAAA,WAAAkB,OAAA,GAAAR,MAAA,CAAAC,WAAA,CAAAE,GAAA,sCAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAR,MAAA,CAAAC,WAAA,CAAAE,GAAA,sCAAAK,OAAA,CAAAH,OAAA,EAAgG;;;;;;IAQ7G/B,EAAA,CAAAC,cAAA,mBAAqF;IAA3BD,EAAA,CAAAI,UAAA,qBAAA+B,oEAAA;MAAAnC,EAAA,CAAAM,aAAA,CAAA8B,IAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAS,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAA2B,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAAlFtC,EAAA,CAAAG,YAAA,EAAqF;IACrFH,EAAA,CAAAC,cAAA,mBAKuC;IADnCD,EAAA,CAAAI,UAAA,qBAAAmC,oEAAA;MAAAvC,EAAA,CAAAM,aAAA,CAAA8B,IAAA;MAAA,MAAAI,OAAA,GAAAxC,EAAA,CAAAS,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAA8B,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAJ9BzC,EAAA,CAAAG,YAAA,EAKuC;;;;IANKH,EAAA,CAAAgB,UAAA,cAAa;IAIrDhB,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,UAAA,cAAa,aAAA0B,MAAA,CAAAf,WAAA,CAAAG,OAAA;;;;;;AD3EzB,OAAM,MAAOa,gBAAgB;EAiBzBC,YACYC,eAAgC,EAChCC,EAAe,EACfC,iBAAoC;IAFpC,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAnB7B,KAAAC,aAAa,GAAY,KAAK;IAU9B,KAAAC,iBAAiB,GAAW,CAAC;EAU1B;EAEHC,QAAQA,CAAA;IACJ,IAAI,CAACC,SAAS,GAAG;MACb,GAAG,IAAI,CAACA,SAAS;MACjBC,OAAO,EAAErD;KACZ;IACD,IAAI,CAAC8C,eAAe,CAACQ,WAAW,EAAE,CAACC,IAAI,CAAEC,IAAI,IAAI;MAC7C,IAAI,CAACJ,SAAS,GAAG;QACb,GAAG,IAAI,CAACA,SAAS;QACjBK,KAAK,EAAED;OACV;IACL,CAAC,CAAC;IAEF,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAACmB,EAAE,CAACW,KAAK,CAAC;MAC7BC,MAAM,EAAE,CAAC,EAAE,EAAE5D,UAAU,CAAC6D,QAAQ,CAAC;MACjCC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC9D,UAAU,CAAC6D,QAAQ,EAAE7D,UAAU,CAAC+D,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7DC,UAAU,EAAE,CAAC,IAAI,EAAE,CAAChE,UAAU,CAAC6D,QAAQ,EAAE7D,UAAU,CAAC+D,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DE,eAAe,EAAE,CAAC,IAAI,EAAE,CAACjE,UAAU,CAAC6D,QAAQ,EAAE7D,UAAU,CAAC+D,GAAG,CAAC,CAAC,CAAC,CAAC;KACnE,CAAC;EACN;EAEAlD,OAAOA,CAAA;IACH,IAAI,CAACgB,WAAW,CAACqC,KAAK,EAAE;IACxB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACjB,aAAa,GAAG,IAAI;EAC7B;EAEAlC,sBAAsBA,CAAA;IAClB,IAAI,CAACiC,iBAAiB,CAACmB,iBAAiB,CAAC,MAAK;MAC1C,IAAI,CAACf,SAAS,CAACK,KAAK,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAACW,MAAM,CAAEC,GAAG,IAAK,CAAC,IAAI,CAAClD,gBAAgB,EAAEmD,QAAQ,CAACD,GAAG,CAAC,CAAC;MAClG,IAAI,CAAClD,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC6B,iBAAiB,CAACuB,gBAAgB,CAAC,mBAAmB,CAAC;IAChE,CAAC,CAAC;EACN;EAEAC,WAAWA,CAACC,IAAI;IACZ,IAAI,CAACtD,gBAAgB,GAAG,CAAC,GAAGsD,IAAI,CAAC;EACrC;EAEAC,WAAWA,CAACR,OAAY;IACpB,IAAI,CAACA,OAAO,GAAG;MAAE,GAAGA;IAAO,CAAE;IAC7B,IAAI,CAACtC,WAAW,CAAC+C,UAAU,CAAC;MACxBhB,MAAM,EAAE,IAAI,CAACO,OAAO,CAACP,MAAM;MAC3BE,WAAW,EAAE,IAAI,CAACK,OAAO,CAACL,WAAW;MACrCE,UAAU,EAAE,IAAI,CAACG,OAAO,CAACH,UAAU;MACnCC,eAAe,EAAE,IAAI,CAACE,OAAO,CAACF;KACjC,CAAC;IACF,IAAI,CAACf,aAAa,GAAG,IAAI;EAC7B;EAEA2B,aAAaA,CAACV,OAAY;IACtB,IAAI,CAAClB,iBAAiB,CAACmB,iBAAiB,CAAC,MAAK;MAC1C,IAAI,CAACf,SAAS,CAACK,KAAK,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAACW,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACQ,UAAU,KAAKX,OAAO,CAACW,UAAU,CAAC;MAClG,IAAI,CAACX,OAAO,GAAG,EAAE;MACjB,IAAI,CAAClB,iBAAiB,CAACuB,gBAAgB,CAAC,kBAAkB,CAAC;IAC/D,CAAC,CAAC;EACN;EAEAhC,UAAUA,CAAA;IACN,IAAI,CAACU,aAAa,GAAG,KAAK;EAC9B;EAEAP,YAAYA,CAAA;IACR,IAAI,IAAI,CAACd,WAAW,CAACkD,KAAK,EAAE;MACxB,IAAI,IAAI,CAACZ,OAAO,CAACW,UAAU,EAAE;QACzB,IAAI,CAACzB,SAAS,CAACK,KAAK,CAAC,IAAI,CAACsB,aAAa,CAAC,IAAI,CAACb,OAAO,CAACW,UAAU,CAAC,CAAC,GAAG,IAAI,CAACX,OAAO;QAChF,IAAI,CAAClB,iBAAiB,CAACuB,gBAAgB,CAAC,YAAY,CAAC;OACxD,MAAM;QACH,IAAI,CAACnB,SAAS,CAACK,KAAK,CAACuB,IAAI,CAAC,IAAI,CAACd,OAAO,CAAC;QACvC,IAAI,CAAClB,iBAAiB,CAACuB,gBAAgB,CAAC,YAAY,CAAC;;MAGzD,IAAI,CAACnB,SAAS,CAACK,KAAK,GAAG,CAAC,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAAC;MAChD,IAAI,CAACR,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACiB,OAAO,GAAG,EAAE;KACpB,MAAM;MACH,IAAI,CAACtC,WAAW,CAACqD,gBAAgB,EAAE,CAAC,CAAC;;EAE7C;;EAEAF,aAAaA,CAACF,UAAkB;IAC5B,IAAIK,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC/B,SAAS,CAACK,KAAK,CAACrC,MAAM,EAAE+D,CAAC,EAAE,EAAE;MAClD,IAAI,IAAI,CAAC/B,SAAS,CAACK,KAAK,CAAC0B,CAAC,CAAC,CAACN,UAAU,KAAKA,UAAU,EAAE;QACnDK,KAAK,GAAGC,CAAC;QACT;;;IAGR,OAAOD,KAAK;EAChB;EAAC,QAAAE,CAAA,G;qBA/GQxC,gBAAgB,EAAA3C,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAtF,EAAA,CAAAoF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxF,EAAA,CAAAoF,iBAAA,CAAAK,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBhD,gBAAgB;IAAAiD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ7BlG,EAAA,CAAAC,cAAA,mBAA6B;QACzBD,EAAA,CAAAqB,UAAA,IAAA+E,uCAAA,yBAKc,IAAAC,uCAAA;QAqBlBrG,EAAA,CAAAG,YAAA,EAAY;QAEZH,EAAA,CAAAC,cAAA,oBAI0C;QAFtCD,EAAA,CAAAI,UAAA,2BAAAkG,8DAAAC,MAAA;UAAA,OAAiBJ,GAAA,CAAA5B,WAAA,CAAAgC,MAAA,CAAmB;QAAA,EAAC,uBAAAC,0DAAAD,MAAA;UAAA,OACxBJ,GAAA,CAAA1B,WAAA,CAAA8B,MAAA,CAAmB;QAAA,EADK,yBAAAE,4DAAAF,MAAA;UAAA,OAEtBJ,GAAA,CAAAxB,aAAA,CAAA4B,MAAA,CAAqB;QAAA,EAFC;QAECvG,EAAA,CAAAG,YAAA,EAAa;QAEvDH,EAAA,CAAAC,cAAA,kBAKyB;QAJrBD,EAAA,CAAAI,UAAA,2BAAAsG,4DAAAH,MAAA;UAAA,OAAAJ,GAAA,CAAAnD,aAAA,GAAAuD,MAAA;QAAA,EAA2B;QAK3BvG,EAAA,CAAAqB,UAAA,IAAAsF,uCAAA,0BAuCc,IAAAC,uCAAA;QAWlB5G,EAAA,CAAAG,YAAA,EAAW;;;QA7DPH,EAAA,CAAAe,SAAA,GAA2B;QAA3Bf,EAAA,CAAAgB,UAAA,kBAAAmF,GAAA,CAAAhD,SAAA,CAA2B;QAO3BnD,EAAA,CAAAe,SAAA,GAA4B;QAA5Bf,EAAA,CAAA6G,UAAA,CAAA7G,EAAA,CAAA8G,eAAA,IAAAC,GAAA,EAA4B;QAD5B/G,EAAA,CAAAgB,UAAA,YAAAmF,GAAA,CAAAnD,aAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}